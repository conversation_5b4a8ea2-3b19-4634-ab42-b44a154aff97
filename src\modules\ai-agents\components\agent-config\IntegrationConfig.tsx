import { Button, Icon, Modal, Typography } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import WebsiteSlideInForm from './WebsiteSlideInForm';
import {
  useAgentWebsites,
  useRemoveWebsite,
} from '../../hooks/useAgentIntegration';
import { NotificationUtil } from '@/shared/utils/notification';

import { IntegrationItem, IntegrationsData } from '../../types/integration';

interface IntegrationConfigProps {
  agentId?: string;
  initialData?: IntegrationsData;
  onSave?: (data: IntegrationsData) => void;
  mode?: 'create' | 'edit';
  typeAgentConfig?: any; // Type agent config để kiểm tra capabilities
}

/**
 * Component hiển thị một tích hợp (Facebook hoặc Website)
 */
const IntegrationItemCard: React.FC<{
  item: IntegrationItem;
  onRemove: (id: string) => void;
}> = ({ item, onRemove }) => {
  const { t } = useTranslation(['common', 'aiAgents']);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  // Xác định màu nền dựa trên loại tích hợp
  const bgColorClass =
    item.type === 'facebook'
      ? 'bg-blue-50 dark:bg-blue-900/10'
      : 'bg-green-50 dark:bg-green-900/10';

  return (
    <div
      className={`flex items-center p-3 ${bgColorClass} rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm`}
    >
      {/* Icon/Avatar */}
      <div className="w-12 h-12 rounded-md overflow-hidden bg-white dark:bg-gray-800 flex items-center justify-center mr-3 flex-shrink-0 border border-gray-200 dark:border-gray-700">
        {item.icon ? (
          <img src={item.icon} alt={item.name} className="w-full h-full object-cover" />
        ) : (
          <Icon
            name={item.type === 'facebook' ? 'facebook' : 'globe'}
            size="md"
            className={item.type === 'facebook' ? 'text-blue-600' : 'text-green-600'}
          />
        )}
      </div>

      {/* Thông tin */}
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
          {item.name}
        </h4>
        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
          <Icon name="document" size="sm" className="mr-1" />
          <span className="truncate">{item.id}</span>
        </div>
      </div>

      {/* Nút xóa */}
      <Button
        variant="ghost"
        size="sm"
        className="ml-2 text-gray-400 hover:text-red-500"
        onClick={() => setShowDeleteModal(true)}
        aria-label="Xóa tích hợp"
      >
        <Icon name="trash" size="sm" />
      </Button>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setShowDeleteModal(false)}>
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button
              variant="danger"
              onClick={() => {
                onRemove(item.id);
                setShowDeleteModal(false);
              }}
            >
              {t('common:delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography className="mb-4">
            {t(
              'aiAgents:integration.confirmDeleteIntegration',
              'Bạn có chắc chắn muốn xóa tích hợp "{{integrationName}}" khỏi Agent không?',
              { integrationName: item.name }
            )}
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
            {t(
              'aiAgents:integration.deleteIntegrationWarning',
              'Hành động này không thể hoàn tác.'
            )}
          </Typography>
        </div>
      </Modal>
    </div>
  );
};

/**
 * Component cấu hình tích hợp cho Agent
 */
const IntegrationConfig: React.FC<IntegrationConfigProps> = ({
  agentId,
  initialData,
  onSave,
  mode = 'create',
  typeAgentConfig
}) => {
  const { t } = useTranslation(['aiAgents', 'common']);

  // State cho dữ liệu tích hợp
  const [configData, setConfigData] = useState<IntegrationsData>(
    initialData || {
      integrations: [],
    }
  );

  // Extract specific config value để tránh re-render liên tục
  const enableWebsiteIntegration = typeAgentConfig?.enableOutputToWebsiteLiveChat;

  // API hooks để lấy dữ liệu từ agent - chỉ gọi khi type config cho phép
  const shouldLoadWebsites = mode === 'edit' && agentId && enableWebsiteIntegration;

  const {
    data: websitesResponse,
    isLoading: isLoadingWebsites,
    error: websitesError,
    refetch: refetchWebsites,
  } = useAgentWebsites(shouldLoadWebsites ? agentId : '');

  // Mutation hooks
  const removeWebsiteMutation = useRemoveWebsite();

  // State cho form slide-in
  const [showWebsiteForm, setShowWebsiteForm] = useState(false);

  // Cập nhật dữ liệu từ API - sử dụng useMemo để tránh re-render liên tục
  const integrationData = useMemo(() => {
    if (mode === 'edit' && agentId && enableWebsiteIntegration && websitesResponse?.websites) {
      const websiteIntegrations = websitesResponse.websites.map(website => ({
        id: website.id,
        name: website.websiteName,
        type: 'website' as const,
        url: website.host,
        icon: undefined,
        isConnected: website.isActive,
        status: (website.verify ? 'active' : 'pending') as 'active' | 'pending' | 'error' | undefined,
      }));
      return { integrations: websiteIntegrations };
    }

    if (mode === 'create') {
      return initialData || { integrations: [] };
    }

    return { integrations: [] };
  }, [mode, agentId, enableWebsiteIntegration, websitesResponse, initialData]);

  // Cập nhật configData khi integrationData thay đổi
  useEffect(() => {
    setConfigData(integrationData);
  }, [integrationData]);

  // Lọc các tích hợp theo loại - sử dụng useMemo để tránh re-render
  const websiteIntegrations = useMemo(() => {
    return configData.integrations.filter(item => item.type === 'website');
  }, [configData.integrations]);



  // Xử lý xóa Website với API call - sử dụng useCallback
  const handleRemoveWebsiteWithAPI = useCallback(async (websiteId: string) => {
    if (!agentId || mode !== 'edit') return;

    try {
      await removeWebsiteMutation.mutateAsync({
        agentId,
        websiteId,
      });

      // Force refetch data để cập nhật giao diện ngay lập tức
      await refetchWebsites();

      NotificationUtil.success({
        message: t('aiAgents:integration.removeWebsiteSuccess'),
      });
    } catch {
      NotificationUtil.error({
        message: t('aiAgents:integration.removeWebsiteError'),
      });
    }
  }, [agentId, mode, removeWebsiteMutation, refetchWebsites, t]);

  // Xử lý khi xóa một tích hợp - phân biệt theo mode - sử dụng useCallback
  const handleRemoveIntegration = useCallback((id: string) => {
    const item = configData.integrations.find(integration => integration.id === id);
    if (!item) return;

    if (mode === 'edit') {
      // Edit mode: gọi API
      if (item.type === 'website') {
        handleRemoveWebsiteWithAPI(id);
      }
    } else {
      // Create mode: chỉ cập nhật local state
      const updatedIntegrations = configData.integrations.filter(
        integration => integration.id !== id
      );
      const newConfigData = { integrations: updatedIntegrations };
      setConfigData(newConfigData);

      // Gọi callback để cập nhật parent component
      if (onSave) {
        onSave(newConfigData);
      }
    }
  }, [configData.integrations, mode, handleRemoveWebsiteWithAPI, onSave]);

  // Xử lý khi thêm một tích hợp mới - sử dụng useCallback
  const handleAddIntegration = useCallback(() => {
    setShowWebsiteForm(true);
  }, []);



  // Callback để nhận dữ liệu từ WebsiteSlideInForm - sử dụng useCallback để tránh re-render
  const handleWebsiteFormSave = useCallback(async (selectedIds: string[]) => {
    if (mode === 'create') {
      // Create mode: cập nhật local state
      const newWebsiteIntegrations = selectedIds.map(id => ({
        id,
        name: `Website ${id}`,
        type: 'website' as const,
        isConnected: true,
        status: 'active' as const,
      }));

      const updatedIntegrations = [
        ...configData.integrations.filter(item => item.type !== 'website'),
        ...newWebsiteIntegrations,
      ];

      const newConfigData = { integrations: updatedIntegrations };
      setConfigData(newConfigData);

      // Gọi callback để cập nhật parent component
      if (onSave) {
        onSave(newConfigData);
      }
    } else {
      // Edit mode: force refetch data để cập nhật giao diện ngay lập tức
      try {
        await refetchWebsites();
      } catch (error) {
        console.error('Error refetching websites:', error);
      }
    }
    setShowWebsiteForm(false);
  }, [mode, configData.integrations, onSave, refetchWebsites]);

  return (
    <>
      <ConfigComponentWrapper
        componentId="integration"
        title={
          <div className="flex items-center">
            <Icon name="globe" size="md" className="mr-2 text-green-600" />
            <span>{t('aiAgents:integration.websiteIntegration')}</span>
          </div>
        }
      >
        <div className="p-4 space-y-6">
          {/* Tiêu đề chính */}
          <div className="mb-6 text-center">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {t('aiAgents:integration.connectAgentToWebsite')}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t('aiAgents:integration.websiteDescription')}
            </p>
          </div>

          {/* Tích hợp Website */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-6 h-6 rounded-full bg-green-600 flex items-center justify-center mr-2">
                  <Icon name="globe" size="sm" className="text-white" />
                </div>
                <h3 className="text-md font-medium text-gray-900 dark:text-gray-100">
                  {t('aiAgents:integration.websites')}
                </h3>
              </div>
              <Button variant="outline" size="sm" onClick={handleAddIntegration}>
                <Icon name="plus" size="sm" className="mr-1" />
                {t('aiAgents:integration.add')}
              </Button>
            </div>

            <div className="space-y-3">
              {isLoadingWebsites ? (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                  <Icon name="loader-circle" size="md" className="animate-spin mx-auto mb-2" />
                  {t('aiAgents:mediaSlideInForm.loadingWebsites')}
                </div>
              ) : websitesError ? (
                <div className="text-center py-4 text-red-500 bg-red-50 dark:bg-red-900/10 rounded-lg border border-red-200 dark:border-red-800">
                  <Icon name="alert-circle" size="md" className="mx-auto mb-2" />
                  {t('aiAgents:mediaSlideInForm.errorLoadingWebsites')}
                </div>
              ) : websiteIntegrations.length > 0 ? (
                websiteIntegrations.map(item => (
                  <IntegrationItemCard
                    key={item.id}
                    item={item}
                    onRemove={handleRemoveIntegration}
                  />
                ))
              ) : (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed border-gray-300 dark:border-gray-700">
                  {t('aiAgents:mediaSlideInForm.noWebsiteIntegrations')}
                </div>
              )}
            </div>
          </div>

          {/* Form slide-in cho Website */}
          <WebsiteSlideInForm
            isVisible={showWebsiteForm}
            onClose={useCallback(() => setShowWebsiteForm(false), [])}
            onSave={handleWebsiteFormSave}
            agentId={agentId ?? ''}
            mode={mode}
          />
        </div>
      </ConfigComponentWrapper>
    </>
  );
};

export default IntegrationConfig;

// Export các interface để có thể sử dụng ở các file khác
export type { IntegrationConfigProps };
