import { Button, Icon } from '@/shared/components/common';
import { useCorsAwareFileUpload } from '@/shared/hooks/common/useCorsAwareFileUpload';
import { useAiAgentNotification } from '../../hooks/useAiAgentNotification';
import { useTranslation } from 'react-i18next';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCreateAgentModularWithService, useGetTypeAgentDetailWithService, useUpdateAgentWithService } from '../../hooks/useAgentService';
import { useUpdateAgentBasicInfo } from '../../hooks/useAgentBasicInfo';
import { useGetAllBaseModels } from '../../hooks/useBaseModel';
import {
    AgentConfigData,
    AgentDetailDto,
    ConvertData,
    IntegrationsData,
    ModelConfigData,
    MultiAgentConfigData,
    ProfileData,
    ResponseData,
    StrategyData,
    TypeAgentConfig,
    TypeProviderEnum
} from '../../types';
import { mapAgentConfigDataToCreateAgentModularDto, mapAgentDetailDtoToAgentConfigData } from '../../utils/agent-data-mappers';
import { TypeAgent } from '../agent-add/TypeAgentCard';
import { AgentConfigHeader } from './AgentConfigHeader';
import { AgentConfigLayout } from './AgentConfigLayout';
import {
    ConvertConfig,
    IntegrationConfig,
    MultiAgentConfig,
    ProfileConfig,
    ResponseConfig,
    StrategyConfig
} from './index';
import ZaloOfficialAccountConfig, { ZaloOfficialAccountConfigData } from './ZaloOfficialAccountConfig';
import FacebookConfig from './FacebookConfig';

// Import logo từ assets
import logoImage from '@/shared/assets/images/logo/logo.png';

/**
 * Mode của form
 */
export type AgentConfigurationMode = 'create' | 'edit';

/**
 * Extended AgentConfigData với các flags hiển thị component
 */
export interface ExtendedAgentConfigData extends AgentConfigData {
    hasProfile?: boolean;
    hasModel?: boolean;
    hasIntegrations?: boolean;
    hasStrategy?: boolean;
    hasConvert?: boolean;
    hasResponse?: boolean;
    hasMultiAgent?: boolean;
    hasZaloOfficialAccount?: boolean;
    customToolIds?: string[];
}

interface AgentConfigurationFormProps {
    // Mode của form
    mode: AgentConfigurationMode;

    // Dữ liệu cho create mode
    typeAgentId?: number;
    typeAgentConfig?: TypeAgentConfig;

    // Dữ liệu cho edit mode
    agentId?: string;
    agentDetailData?: AgentDetailDto;

    // Dữ liệu hiện tại của form (có thể được override bởi agentDetailData)
    initialData?: ExtendedAgentConfigData;

    // Callbacks
    onSuccess?: (agentId?: string) => void;
    onBack?: () => void;
    onCancel?: () => void;

    // Dữ liệu bổ sung
    availableAgents?: TypeAgent[];
}

export const AgentConfigurationForm: React.FC<AgentConfigurationFormProps> = ({
    mode,
    typeAgentId,
    typeAgentConfig,
    agentId,
    agentDetailData,
    initialData,
    onSuccess,
    onBack,
    onCancel,
    availableAgents = []
}) => {
    const { t } = useTranslation(['aiAgents', 'common']);
    const {
        createSuccess,
        createError,
        updateSuccess,
        updateError,
        warning
    } = useAiAgentNotification();

    // Debug re-render
    console.log('🔄 AgentConfigurationForm RENDER:', {
        timestamp: new Date().toISOString(),
        mode,
        agentId,
        hasInitialData: !!initialData,
        hasTypeAgentConfig: !!typeAgentConfig,
        hasAgentDetailData: !!agentDetailData
    });
    const navigate = useNavigate();
    const fileInputRef = useRef<HTMLInputElement>(null);

    // State để lưu dữ liệu form hiện tại
    const [agentData, setAgentData] = useState<ExtendedAgentConfigData | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // State để lưu file avatar thực tế mà user upload
    const [avatarFile, setAvatarFile] = useState<File | null>(null);

    // API hooks
    const createAgentMutation = useCreateAgentModularWithService();
    const updateAgentMutation = useUpdateAgentWithService();
    const updateBasicInfoMutation = useUpdateAgentBasicInfo();
    // XÓA: Không cần deleteAgentMutation nữa

    // Hook để lấy danh sách models
    const { data: baseModelsResponse, isLoading: isLoadingModels } = useGetAllBaseModels();

    // Hook để lấy thông tin TypeAgent detail
    const { data: typeAgentDetailResponse } = useGetTypeAgentDetailWithService(typeAgentId);

    // Hook để upload file với TaskQueue
    const fileUploadWithQueue = useCorsAwareFileUpload({
        defaultTaskTitle: 'Upload avatar',
        autoAddToQueue: true,
    });

    // Function để upload avatar sau khi tạo agent
    const uploadAvatarAfterCreate = useCallback(async (avatarUploadUrl: string): Promise<void> => {
        try {
            let file: File;
            let taskTitle: string;

            // Kiểm tra xem có file avatar thực tế không
            if (avatarFile) {
                // Sử dụng file thực tế mà user đã upload
                console.log('Uploading user-selected avatar file');
                file = avatarFile;
                taskTitle = 'Upload user avatar';
            } else {
                // Fallback - upload logo mặc định
                console.log('Uploading default logo as avatar');
                const response = await fetch(logoImage);
                const blob = await response.blob();
                file = new File([blob], 'default-avatar.png', { type: 'image/png' });
                taskTitle = 'Upload default avatar';
            }

            console.log('Uploading file:', file.name, 'Size:', file.size, 'Type:', file.type);

            // Upload file với URL từ API response
            await fileUploadWithQueue.uploadToUrlWithQueue({
                file,
                presignedUrl: avatarUploadUrl,
                taskTitle,
                taskDescription: `Kích thước: ${(file.size / 1024).toFixed(1)} KB`,
            });
        } catch (error) {
            console.error('Error uploading avatar:', error);
            throw error;
        }
    }, [fileUploadWithQueue, avatarFile]);

    // Debug log cho typeAgentConfig
    console.log('AgentConfigurationForm - Props:', {
        mode,
        typeAgentId,
        typeAgentConfig,
        agentId,
        agentDetailData,
        initialData
    });

    // Khởi tạo dữ liệu form dựa trên mode
    useEffect(() => {
        console.log('🔄 AgentConfigurationForm useEffect triggered:', {
            timestamp: new Date().toISOString(),
            mode,
            hasAgentDetailData: !!agentDetailData,
            hasInitialData: !!initialData,
            hasTypeAgentConfig: !!typeAgentConfig,
            typeAgentDetailResponse: typeAgentDetailResponse?.result,
            isLoadingModels
        });

        // Tránh re-render không cần thiết
        if (agentData) {
            console.log('AgentData already exists, skipping initialization');
            return;
        }

        // Lấy config từ typeAgentDetailResponse nếu có
        const typeConfig = typeAgentDetailResponse?.result?.config || typeAgentConfig;
        console.log('🎯 Type config being used:', typeConfig);

        if (mode === 'edit' && agentDetailData) {
            // Edit mode: map từ AgentDetailDto sang AgentConfigData
            const mappedData = mapAgentDetailDtoToAgentConfigData(agentDetailData);
            setAgentData({
                ...mappedData,
                // Thêm flags hiển thị component (có thể lấy từ typeAgentConfig nếu có)
                hasProfile: true,
                hasModel: true,
                hasIntegrations: true,
                hasStrategy: true,
                hasConvert: true,
                hasResponse: true,
                hasMultiAgent: false,
            });
        } else if (initialData) {
            // Sử dụng initialData nếu có (từ useAgentEditData hoặc create mode)
            setAgentData({
                ...initialData,
                // Thêm flags từ typeConfig
                hasProfile: typeConfig?.enableAgentProfileCustomization ?? true,
                hasModel: true,
                hasIntegrations: typeConfig?.enableOutputToWebsiteLiveChat ?? true,
                hasStrategy: typeConfig?.enableDynamicStrategyExecution ?? true,
                hasConvert: typeConfig?.enableTaskConversionTracking ?? true,
                hasResponse: typeConfig?.enableResourceUsage ?? true,
                hasMultiAgent: typeConfig?.enableMultiAgentCollaboration ?? false,
            });
        } else if (mode === 'create' && !isLoadingModels && baseModelsResponse?.result?.items) {
            // Create mode: khởi tạo với dữ liệu mặc định
            // Lấy model đầu tiên từ API
            const firstModel = baseModelsResponse.result.items[0];
            const defaultModelId = firstModel?.model_id || 'gpt-4';
            const defaultProvider = firstModel?.providerType || TypeProviderEnum.OPENAI;

            const defaultData: ExtendedAgentConfigData = {
                name: 'Agent mới',
                avatar: logoImage, // Sử dụng logo local, sẽ upload sau khi tạo agent
                profile: {
                    birthDate: '01/01/2000',
                    gender: 'Nam',
                    language: 'Tiếng Việt',
                    education: 'Đại học',
                    country: 'Việt Nam',
                    position: 'AI Assistant',
                    skills: [],
                    personality: 'Thân thiện, nhiệt tình, chuyên nghiệp',
                },
                modelConfig: {
                    provider: defaultProvider,
                    modelId: defaultModelId,
                    vectorStore: 'pinecone',
                    maxTokens: 1000,
                    temperature: 0.7,
                    topP: 0.9,
                    topK: 40,
                },
                integrations: { integrations: [] },
                strategy: {
                    strategyId: null // Mặc định không chọn strategy nào
                },
                response: { media: [], urls: [], products: [] },
                convert: { fields: [] },
                multiAgent: { agents: [] },
                customToolIds: [],
                zaloOfficialAccountIds: [],
                // Flags từ typeConfig với defaults để test
                hasProfile: typeConfig?.enableAgentProfileCustomization ?? true,
                hasModel: true,
                hasIntegrations: typeConfig?.enableOutputToWebsiteLiveChat ?? true,
                hasStrategy: typeConfig?.enableDynamicStrategyExecution ?? true,
                hasConvert: typeConfig?.enableTaskConversionTracking ?? true,
                hasResponse: typeConfig?.enableResourceUsage ?? true,
                hasMultiAgent: typeConfig?.enableMultiAgentCollaboration ?? true,
            };
            setAgentData(defaultData);
        }
    }, [
        mode,
        agentDetailData,
        initialData,
        isLoadingModels,
        // Chỉ theo dõi những giá trị thực sự cần thiết và stable
        baseModelsResponse?.result?.items?.length,
        typeAgentDetailResponse?.result?.id,
        typeAgentConfig?.enableAgentProfileCustomization,
        agentData // Thêm agentData để tránh re-init
    ]);

    // Helper function để cập nhật agentData
    const updateAgentData = (updates: Partial<ExtendedAgentConfigData>) => {
        setAgentData(prev => prev ? { ...prev, ...updates } : null);
    };



    // Xử lý khi nhấp vào overlay để tải lên avatar
    const handleAvatarUpload = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    // Xử lý khi người dùng đã chọn file ảnh
    const handleAvatarFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            const imageUrl = URL.createObjectURL(file);
            updateAgentData({ avatar: imageUrl });
            // Lưu file thực tế để upload sau này
            setAvatarFile(file);
        }
    };

    // Xử lý khi cập nhật tên agent qua InlineEditInput
    const handleNameUpdate = useCallback((newName: string) => {
        updateAgentData({ name: newName });
    }, []);

    // Validation cho tên agent
    const validateAgentName = useCallback((name: string) => {
        if (!name.trim()) {
            return 'Tên agent không được để trống';
        }
        if (name.trim().length < 2) {
            return 'Tên agent phải có ít nhất 2 ký tự';
        }
        if (name.trim().length > 100) {
            return 'Tên agent không được vượt quá 100 ký tự';
        }
        return null;
    }, []);

    // Xử lý khi cập nhật profile
    const handleProfileUpdate = (profileData: ProfileData) => {
        updateAgentData({ profile: profileData });
    };

    // Xử lý khi cập nhật cấu hình model
    const handleModelConfigUpdate = (modelConfigData: ModelConfigData) => {
        updateAgentData({ modelConfig: modelConfigData });
    };

    // Xử lý khi cập nhật tích hợp
    const handleIntegrationsUpdate = (integrationsData: IntegrationsData) => {
        console.log('🔄 AgentConfigurationForm handleIntegrationsUpdate:', integrationsData);
        updateAgentData({ integrations: integrationsData });
    };

    // Xử lý khi cập nhật chiến lược
    const handleStrategyUpdate = (strategyData: StrategyData) => {
        updateAgentData({ strategy: strategyData });
    };

    // Xử lý khi cập nhật tài nguyên phản hồi
    const handleResponseUpdate = (responseData: ResponseData) => {
        updateAgentData({ response: responseData });
    };

    // Xử lý khi cập nhật dữ liệu chuyển đổi
    const handleConvertUpdate = (convertData: ConvertData) => {
        updateAgentData({ convert: convertData });
    };

    // Xử lý khi cập nhật multi-agent
    const handleMultiAgentUpdate = (multiAgentData: MultiAgentConfigData) => {
        updateAgentData({ multiAgent: multiAgentData });
    };

    // Xử lý khi cập nhật custom tools
   

    // Xử lý khi cập nhật Zalo Official Accounts
    const handleZaloOfficialAccountUpdate = (zaloData: ZaloOfficialAccountConfigData) => {
        updateAgentData({ zaloOfficialAccountIds: zaloData.zaloOfficialAccountIds });
    };

    // Xử lý khi lưu
    const handleSave = async () => {
        if (!agentData) return;

        setIsLoading(true);
        try {
            if (mode === 'create') {
                // Tạo mới agent
                if (!typeAgentId) {
                    throw new Error('TypeAgent ID is required for create mode');
                }

                // Lấy config từ typeAgentDetailResponse nếu có, fallback về typeAgentConfig
                const typeConfig = typeAgentDetailResponse?.result?.config || typeAgentConfig;

                // Map config sang format mà mapper expect
                const mappedTypeConfig = typeConfig ? {
                    hasProfile: typeConfig.enableAgentProfileCustomization ?? false,
                    hasOutput: typeConfig.enableResourceUsage ?? false,
                    hasResources: typeConfig.enableOutputToWebsiteLiveChat ?? false,
                    hasStrategy: typeConfig.enableDynamicStrategyExecution ?? false,
                    hasMultiAgent: typeConfig.enableMultiAgentCollaboration ?? false,
                    hasConversion: typeConfig.enableTaskConversionTracking ?? false,
                } : undefined;

                const createData = mapAgentConfigDataToCreateAgentModularDto(agentData, typeAgentId, mappedTypeConfig, avatarFile);
                const response = await createAgentMutation.mutateAsync(createData);

                // Upload avatar nếu có avatarUploadUrl
                if (response.result?.avatarUploadUrl) {
                    console.log('Starting avatar upload...');
                    try {
                        await uploadAvatarAfterCreate(response.result.avatarUploadUrl);
                        console.log('Avatar upload completed successfully');
                    } catch (uploadError) {
                        console.error('Avatar upload failed:', uploadError);
                        // Không throw error vì agent đã được tạo thành công
                        warning({
                            message: t('aiAgents:notification.uploadError'),
                            duration: 5000,
                        });
                    }
                } else {
                    console.log('Skipping avatar upload - no upload URL provided');
                }

                createSuccess(t('aiAgents:aiAgents'));

                onSuccess?.(response.result?.id);
            } else if (mode === 'edit') {
                // Cập nhật agent - sử dụng basic-info endpoint
                if (!agentId) {
                    throw new Error('Agent ID is required for edit mode');
                }

                // Chuẩn bị data cho basic-info endpoint
                const modelConfig = agentData.modelConfig;
                const basicInfoData = {
                    name: agentData.name,
                    avatarFile: avatarFile ? {
                        fileName: avatarFile.name,
                        mimeType: avatarFile.type
                    } : null,
                    userModelId: modelConfig?.keyLlmId && modelConfig.keyLlmId !== 'redai' ? modelConfig.modelId : null,
                    keyLlmId: modelConfig?.keyLlmId && modelConfig.keyLlmId !== 'redai' ? modelConfig.keyLlmId : null,
                    systemModelId: modelConfig?.keyLlmId === 'redai' ? modelConfig.modelId : null,
                    modelFineTuneId: null, // Chưa support
                    modelConfig: {
                        temperature: modelConfig?.temperature || 1,
                        top_p: modelConfig?.topP || 1,
                        top_k: modelConfig?.topK || 1,
                        max_tokens: modelConfig?.maxTokens || 1000
                    },
                    instruction: modelConfig?.instruction || '',
                    vectorStoreId: modelConfig?.vectorStore && modelConfig.vectorStore !== 'pinecone' ? modelConfig.vectorStore : null
                };

                const response = await updateBasicInfoMutation.mutateAsync({
                    agentId,
                    data: basicInfoData
                });

                // Upload avatar nếu có file và có urlUpload từ response
                if (avatarFile && response.urlUpload) {
                    console.log('Starting avatar upload after basic-info update...');
                    console.log('Avatar URL from response:', response.urlUpload);
                    console.log('Avatar file:', avatarFile);

                    try {
                        // Sử dụng fetch để upload trực tiếp lên S3
                        const uploadResponse = await fetch(response.urlUpload, {
                            method: 'PUT',
                            body: avatarFile,
                            headers: {
                                'Content-Type': avatarFile.type,
                            },
                        });

                        if (!uploadResponse.ok) {
                            throw new Error(`Upload failed with status: ${uploadResponse.status}`);
                        }

                        console.log('Avatar upload completed successfully');

                        // Hiển thị thông báo thành công
                        updateSuccess(t('aiAgents:aiAgents'));
                    } catch (uploadError) {
                        console.error('Avatar upload failed:', uploadError);
                        // Không throw error vì basic-info đã được cập nhật thành công
                        warning({
                            message: t('aiAgents:notification.uploadError'),
                            duration: 5000,
                        });
                    }
                } else {
                    console.log('Skipping avatar upload - no file or no avatar URL');
                    console.log('avatarFile:', avatarFile);
                    console.log('response.urlUpload:', response.urlUpload);

                    // Hiển thị thông báo thành công cho trường hợp không có avatar
                    updateSuccess(t('aiAgents:aiAgents'));
                }

                // Edit mode: gọi onSuccess để trigger reload data (không navigate)
                onSuccess?.(agentId);
            }
        } catch (error: any) {
            console.error('Save agent error:', error);

            // Extract error message từ API response
            let errorMessage: string | undefined;
            if (error?.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error instanceof Error) {
                errorMessage = error.message;
            }

            if (mode === 'create') {
                createError(t('aiAgents:aiAgents'), errorMessage);
            } else {
                updateError(t('aiAgents:aiAgents'), errorMessage);
            }
        } finally {
            setIsLoading(false);
        }
    };

    // XÓA: Không cần handleDelete nữa vì đã remove nút "Xóa Agent"

    // Xử lý khi hủy
    const handleCancel = () => {
        if (onCancel) {
            onCancel();
        } else {
            navigate('/ai-agents');
        }
    };

    // Kiểm tra loading state
    const isSaving = createAgentMutation.isPending || updateAgentMutation.isPending || updateBasicInfoMutation.isPending || isLoading;

    // Kiểm tra có thay đổi không (đơn giản hóa - luôn có thay đổi nếu có dữ liệu)
    const hasChanges = !!agentData;

    // Không hiển thị loading ở đây nữa - AgentEditPage đã handle loading
    // AgentEditPage sẽ đảm bảo chỉ render AgentConfigurationForm khi đã có đủ data
    if (!agentData) {
        console.warn('AgentConfigurationForm rendered without agentData - this should not happen');
        return null;
    }

    return (
        <div>
            <div className="py-4 sm:py-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-4 sm:gap-0">
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onBack}
                            className="flex items-center space-x-2"
                        >
                            <Icon name="arrow-left" size="sm" />
                            <span>{t('common:back')}</span>
                        </Button>
                        <h1 className="text-xl sm:text-2xl font-bold">
                            {mode === 'create' ? t('aiAgents:agentConfigurationForm.createAgent') : t('aiAgents:agentConfigurationForm.editAgent')}
                        </h1>
                    </div>
                </div>

                {/* Layout mới với Left Panel + Right Content */}
                <AgentConfigLayout
                    typeAgentConfig={typeAgentConfig || {}}
                >
                    {{
                        leftPanel: (
                            <AgentConfigHeader
                                agentData={agentData}
                                typeAgentName={typeAgentDetailResponse?.result?.name || 'Unknown Type'}
                                onAvatarUpload={handleAvatarUpload}
                                onAvatarFileChange={handleAvatarFileChange}
                                onNameUpdate={handleNameUpdate}
                                onModelConfigUpdate={handleModelConfigUpdate}
                                validateAgentName={validateAgentName}
                                fileInputRef={fileInputRef}
                                onSave={handleSave}
                                onCancel={handleCancel}
                                showProviderSelection={false}
                                // XÓA: Không truyền onDelete nữa
                                isSaving={isSaving}
                                hasChanges={hasChanges}
                                mode={mode}
                                agentId={agentId || ''}
                            />
                        ),

                        profileConfig: (typeAgentDetailResponse?.result?.config?.enableAgentProfileCustomization || typeAgentConfig?.enableAgentProfileCustomization) ? (
                            <ProfileConfig
                                agentId={agentId || ''}
                                initialData={agentData.profile}
                                onSave={handleProfileUpdate}
                                mode={mode}
                                typeAgentConfig={typeAgentDetailResponse?.result?.config || typeAgentConfig}
                            />
                        ) : undefined,

                        integrationConfig: (typeAgentDetailResponse?.result?.config?.enableOutputToWebsiteLiveChat || typeAgentConfig?.enableOutputToWebsiteLiveChat) ? (
                            <IntegrationConfig
                                agentId={agentId || ''}
                                mode={mode}
                                initialData={agentData.integrations}
                                onSave={handleIntegrationsUpdate}
                                typeAgentConfig={typeAgentDetailResponse?.result?.config || typeAgentConfig}
                            />
                        ) : undefined,

                        facebookConfig: (typeAgentDetailResponse?.result?.config?.enableOutputToMessenger || typeAgentConfig?.enableOutputToMessenger) ? (
                            <FacebookConfig
                                agentId={agentId || ''}
                                mode={mode}
                                initialData={agentData.integrations}
                                onSave={handleIntegrationsUpdate}
                                typeAgentConfig={typeAgentDetailResponse?.result?.config || typeAgentConfig}
                            />
                        ) : undefined,

                        strategyConfig: (typeAgentDetailResponse?.result?.config?.enableDynamicStrategyExecution || typeAgentConfig?.enableDynamicStrategyExecution) ? (
                            <StrategyConfig
                                initialData={agentData.strategy}
                                onSave={handleStrategyUpdate}
                            />
                        ) : undefined,

                        responseConfig: (typeAgentDetailResponse?.result?.config?.enableResourceUsage || typeAgentConfig?.enableResourceUsage) ? (
                            <ResponseConfig
                                agentId={agentId || undefined}
                                mode={mode as 'create' | 'edit'}
                                initialData={agentData.response}
                                onSave={handleResponseUpdate}
                            />
                        ) : undefined,

                        convertConfig: (typeAgentDetailResponse?.result?.config?.enableTaskConversionTracking || typeAgentConfig?.enableTaskConversionTracking) ? (
                            <ConvertConfig
                                agentId={agentId || ''}
                                mode={mode}
                                initialData={agentData.convert}
                                onSave={handleConvertUpdate}
                            />
                        ) : undefined,

                        multiAgentConfig: (typeAgentDetailResponse?.result?.config?.enableMultiAgentCollaboration || typeAgentConfig?.enableMultiAgentCollaboration) ? (
                            <MultiAgentConfig
                                initialData={agentData.multiAgent}
                                onSave={handleMultiAgentUpdate}
                                availableAgents={availableAgents}
                            />
                        ) : undefined,

                     

                        zaloOfficialAccountConfig: (typeAgentDetailResponse?.result?.config?.enableOutputToZaloOA || typeAgentConfig?.enableOutputToZaloOA) ? (
                            <ZaloOfficialAccountConfig
                                initialData={{ zaloOfficialAccountIds: agentData.zaloOfficialAccountIds || [] }}
                                onSave={handleZaloOfficialAccountUpdate}
                                mode={mode}
                                agentId={agentId}
                            />
                        ) : undefined,
                    }}
                </AgentConfigLayout>
            </div>
        </div>
    );
};

export default AgentConfigurationForm;
